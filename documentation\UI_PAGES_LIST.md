# UI Pages List - Enterprise Portfolio Management System

## 📱 Authentication & Onboarding Pages

### 1. Authentication Flow
- **Login Page** (`/login`)
  - Email/password form
  - Remember me checkbox
  - Forgot password link
  - Social login options (if applicable)

- **Registration Page** (`/register`)
  - User registration form
  - Email verification flow
  - Password strength indicator
  - Terms and conditions acceptance

- **Forgot Password Page** (`/forgot-password`)
  - Email input form
  - Reset instructions display

- **Reset Password Page** (`/reset-password`)
  - New password form
  - Password confirmation
  - Security token validation

- **Email Verification Page** (`/verify-email`)
  - Verification status display
  - Resend verification option

### 2. Onboarding Flow
- **Welcome Page** (`/welcome`)
  - Platform introduction
  - Getting started guide

- **Profile Setup Page** (`/onboarding/profile`)
  - Personal information form
  - Avatar upload
  - Preferences setup

## 🏠 Main Application Pages

### 3. Dashboard & Overview
- **Main Dashboard** (`/dashboard`)
  - Portfolio overview cards
  - Performance charts
  - Recent activity feed
  - Quick action buttons
  - Market summary widget

- **Portfolio Overview** (`/portfolio`)
  - Portfolio performance summary
  - Asset allocation charts
  - Holdings table
  - Performance metrics

### 4. User Management
- **User Profile Page** (`/profile`)
  - Personal information display/edit
  - Avatar management
  - Contact information

- **Account Settings** (`/settings`)
  - General settings
  - Notification preferences
  - Privacy settings
  - Security settings

- **Security Settings** (`/settings/security`)
  - Password change
  - Two-factor authentication
  - Login history
  - Active sessions

## 💼 Portfolio Management Pages

### 5. Portfolio Operations
- **Portfolio Details** (`/portfolio/[id]`)
  - Detailed portfolio view
  - Holdings breakdown
  - Performance analytics
  - Transaction history

- **Create Portfolio** (`/portfolio/create`)
  - Portfolio creation form
  - Initial allocation setup
  - Risk profile selection

- **Edit Portfolio** (`/portfolio/[id]/edit`)
  - Portfolio modification form
  - Allocation adjustments
  - Settings updates

### 6. Holdings & Positions
- **Holdings List** (`/holdings`)
  - All positions overview
  - Sorting and filtering
  - Performance indicators

- **Position Details** (`/position/[symbol]`)
  - Individual position analysis
  - Price charts
  - Transaction history
  - Performance metrics

## 🔗 Broker Integration Pages

### 7. Broker Management
- **Broker Connections** (`/brokers`)
  - Connected brokers list
  - Connection status indicators
  - Add new broker button

- **Add Broker Connection** (`/brokers/connect`)
  - Broker selection
  - Authentication flow
  - API key/OAuth setup

- **Broker Details** (`/brokers/[id]`)
  - Broker information
  - Connection settings
  - Account details
  - Sync status

- **Broker Settings** (`/brokers/[id]/settings`)
  - Connection configuration
  - Sync preferences
  - Risk management settings

## 📈 Trading Pages

### 8. Trading Operations
- **Trading Dashboard** (`/trading`)
  - Active orders
  - Recent trades
  - Market overview
  - Quick trade panel

- **Place Order** (`/trading/order`)
  - Order placement form
  - Market/limit order options
  - Order preview and confirmation

- **Order History** (`/trading/orders`)
  - All orders list
  - Order status tracking
  - Filtering and search

- **Trade History** (`/trading/history`)
  - Executed trades list
  - Trade details
  - Performance impact

## 👥 Copy Trading Pages

### 9. Master Trader Features
- **Master Trader Setup** (`/copy-trading/master/setup`)
  - Master account registration
  - Strategy description
  - Fee structure setup
  - Terms configuration

- **Master Dashboard** (`/copy-trading/master`)
  - Follower overview
  - Performance metrics
  - Earnings summary
  - Strategy management

- **Follower Management** (`/copy-trading/master/followers`)
  - Followers list
  - Individual follower details
  - Allocation management
  - Communication tools

### 10. Copy Trading Discovery
- **Master Traders List** (`/copy-trading/discover`)
  - Available master traders
  - Performance rankings
  - Filtering and search
  - Strategy categories

- **Master Trader Profile** (`/copy-trading/master/[id]`)
  - Detailed trader information
  - Performance history
  - Strategy description
  - Follower reviews

### 11. Copy Trading Management
- **My Copy Trading** (`/copy-trading/my-copies`)
  - Active copy relationships
  - Performance comparison
  - Allocation settings
  - Copy history

- **Copy Trading Setup** (`/copy-trading/setup/[masterId]`)
  - Connection configuration
  - Allocation settings
  - Risk management
  - Agreement acceptance

## 📊 Analytics & Reporting Pages

### 12. Performance Analytics
- **Performance Dashboard** (`/analytics/performance`)
  - Comprehensive performance charts
  - Return calculations
  - Benchmark comparisons
  - Risk metrics

- **Portfolio Analytics** (`/analytics/portfolio`)
  - Asset allocation analysis
  - Sector distribution
  - Geographic allocation
  - Correlation analysis

- **Copy Trading Analytics** (`/analytics/copy-trading`)
  - Master trader performance
  - Copy effectiveness
  - Slippage analysis
  - Fee impact

### 13. Reporting
- **Reports Dashboard** (`/reports`)
  - Available reports list
  - Scheduled reports
  - Report history

- **Generate Report** (`/reports/generate`)
  - Report type selection
  - Date range picker
  - Customization options
  - Export format selection

- **Report Viewer** (`/reports/[id]`)
  - Report display
  - Export options
  - Sharing functionality

## 🔧 Administrative Pages

### 14. System Management
- **Notifications** (`/notifications`)
  - Notification center
  - Read/unread status
  - Notification settings

- **Help & Support** (`/help`)
  - FAQ section
  - Contact support
  - Documentation links
  - Video tutorials

- **System Status** (`/status`)
  - Platform status
  - Broker connectivity
  - Maintenance notifications

## 📱 Mobile-Specific Pages

### 15. Mobile Optimized Views
- **Mobile Dashboard** (`/mobile/dashboard`)
- **Mobile Trading** (`/mobile/trading`)
- **Mobile Portfolio** (`/mobile/portfolio`)
- **Mobile Notifications** (`/mobile/notifications`)

## 🚨 Error & Utility Pages

### 16. Error Handling
- **404 Not Found** (`/404`)
- **500 Server Error** (`/500`)
- **Maintenance Mode** (`/maintenance`)
- **Unauthorized Access** (`/unauthorized`)

## 📋 Page Implementation Priority

### Phase 1 (Critical - MVP)
1. Login/Registration pages
2. Main Dashboard
3. Portfolio Overview
4. Broker Connections
5. Basic Trading pages

### Phase 2 (High Priority)
6. Copy Trading Discovery
7. Master Trader Setup
8. Performance Analytics
9. User Profile/Settings

### Phase 3 (Medium Priority)
10. Advanced Analytics
11. Reporting pages
12. Administrative pages
13. Mobile optimizations

---

**Total Pages**: ~45-50 unique pages/views
**Estimated UI Development Time**: 16-20 weeks
**Component Reusability**: High (shared layouts, forms, charts)
