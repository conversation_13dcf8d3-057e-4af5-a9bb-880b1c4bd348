"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/src/presentation/components/ui/card";
import { But<PERSON> } from "@/src/presentation/components/ui/button";
import {
  Target,
  DollarSign,
  Clock,
  Users2,
  ArrowRight,
  CheckCircle
} from "lucide-react";

const userTypes = [
  {
    icon: Target,
    title: "Individual Investors",
    description: "Take control of your investments with professional-grade tools",
    benefits: [
      "AI-powered investment recommendations",
      "Copy successful trading strategies",
      "Real-time portfolio monitoring",
      "Risk management tools"
    ],
    cta: "Start Investing Smarter"
  },
  {
    icon: Users2,
    title: "Professional Traders",
    description: "Scale your trading operation and monetize your expertise",
    benefits: [
      "Become a master trader",
      "Earn from followers",
      "Advanced analytics dashboard",
      "Multi-account management"
    ],
    cta: "Become a Master Trader"
  },
  {
    icon: DollarSign,
    title: "Financial Advisors",
    description: "Manage client portfolios with institutional-grade infrastructure",
    benefits: [
      "Client portfolio management",
      "Compliance reporting",
      "White-label solutions",
      "API integrations"
    ],
    cta: "Manage Client Portfolios"
  }
];

const stats = [
  {
    icon: DollarSign,
    value: "$2.5B+",
    label: "Assets Under Management"
  },
  {
    icon: Users2,
    value: "10,000+",
    label: "Active Traders"
  },
  {
    icon: Target,
    value: "15%",
    label: "Average Annual Return"
  },
  {
    icon: Clock,
    value: "<100ms",
    label: "Order Execution Time"
  }
];

export function BenefitsSection() {
  return (
    <section id="benefits" className="py-24 px-4 sm:px-6 lg:px-8 bg-muted/30">
      <div className="max-w-7xl mx-auto">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Built for Every Investor
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Whether you're just starting or managing millions, our platform scales with your needs
          </p>
        </div>

        {/* User types */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
          {userTypes.map((userType, index) => (
            <Card
              key={userType.title}
              className="group hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border-border/50 bg-card/80 backdrop-blur-sm"
              style={{
                animationDelay: `${index * 150}ms`
              }}
            >
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <userType.icon className="w-8 h-8 text-primary" />
                </div>
                <CardTitle className="text-xl font-semibold">
                  {userType.title}
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  {userType.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {userType.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-muted-foreground">{benefit}</span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full group mt-6">
                  {userType.cta}
                  <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats section */}
        <div className="bg-card/50 backdrop-blur-sm rounded-2xl border border-border/50 p-8 lg:p-12">
          <div className="text-center mb-12">
            <h3 className="text-2xl sm:text-3xl font-bold mb-4">
              Trusted by Thousands of Investors
            </h3>
            <p className="text-muted-foreground">
              Join a growing community of successful traders and investors
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div
                key={stat.label}
                className="text-center group"
                style={{
                  animationDelay: `${index * 100}ms`
                }}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-6 h-6 text-primary" />
                </div>
                <div className="text-2xl sm:text-3xl font-bold text-foreground mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
