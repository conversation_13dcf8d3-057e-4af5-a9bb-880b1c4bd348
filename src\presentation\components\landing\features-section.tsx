"use client";

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/src/presentation/components/ui/card";
import {
  Brain,
  Users,
  Link,
  BarChart3,
  Shield,
  Zap,
  TrendingUp,
  Globe,
  Lock
} from "lucide-react";

const features = [
  {
    icon: Brain,
    title: "AI-Powered Trading",
    description: "Advanced machine learning algorithms analyze market patterns and provide intelligent trading recommendations.",
    color: "text-purple-500"
  },
  {
    icon: Users,
    title: "Copy Trading",
    description: "Follow successful traders automatically. Connect as a master trader or copy proven strategies.",
    color: "text-blue-500"
  },
  {
    icon: Link,
    title: "Multi-Broker Integration",
    description: "Connect multiple trading accounts from Alpaca, Interactive Brokers, TD Ameritrade, and more.",
    color: "text-green-500"
  },
  {
    icon: BarChart3,
    title: "Advanced Analytics",
    description: "Real-time performance tracking, risk analysis, and comprehensive portfolio insights.",
    color: "text-orange-500"
  },
  {
    icon: Shield,
    title: "Enterprise Security",
    description: "Bank-level encryption, secure API connections, and institutional-grade data protection.",
    color: "text-red-500"
  },
  {
    icon: Zap,
    title: "Real-Time Execution",
    description: "Lightning-fast order execution with real-time market data and instant notifications.",
    color: "text-yellow-500"
  }
];

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        {/* Section header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-4">
            <span className="bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Powerful Features
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Everything you need to manage your portfolio like a professional trader
          </p>
        </div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card
              key={feature.title}
              className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-border/50 bg-card/50 backdrop-blur-sm"
              style={{
                animationDelay: `${index * 100}ms`
              }}
            >
              <CardHeader>
                <div className={`w-12 h-12 rounded-lg bg-muted/50 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className={`w-6 h-6 ${feature.color}`} />
                </div>
                <CardTitle className="text-xl font-semibold group-hover:text-primary transition-colors">
                  {feature.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional feature highlights */}
        <div className="mt-20 grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="text-center p-6">
            <div className="w-16 h-16 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-lg font-semibold mb-2">99.9% Uptime</h3>
            <p className="text-muted-foreground">Reliable platform with enterprise-grade infrastructure</p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500/20 to-green-500/5 rounded-full flex items-center justify-center mx-auto mb-4">
              <Globe className="w-8 h-8 text-green-500" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Global Markets</h3>
            <p className="text-muted-foreground">Trade across multiple exchanges and asset classes</p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500/20 to-blue-500/5 rounded-full flex items-center justify-center mx-auto mb-4">
              <Lock className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-lg font-semibold mb-2">SOC 2 Compliant</h3>
            <p className="text-muted-foreground">Meets the highest security and compliance standards</p>
          </div>
        </div>
      </div>
    </section>
  );
}
